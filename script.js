// SORT AN ARRAY
// logic using two loops 
// var arr = [5,4,3,2]
// for(var j=0;j<arr.length;j++){
//     for(var i=0;i<arr.length-1;i++){
//     if(arr[i] > arr[i+1]){
//     var temp = arr[i]
//     arr[i] = arr[i+1]
//     arr[i+1] = temp;
//     }
// }
// }
// console.log(arr)




//  SORT STRING 
// let str = "umesh";
// let sortedStr = str.split('').sort().join('');
// console.log(sortedStr);




//rverse string
// let str2 = "Umesh"
// let reversestr = str2.split('').reverse().join('');
// console.log(reversestr)




//reverse number
// let num = 123;
// let reversenum = parseInt(num.toString().split('').reverse().join(''));
// console.log(reversenum);




//write a program to create a copy of an array withought mutating the original.
//usnig spred operator 
// var arr = [11,22,33,55,44]
// var arr2 = [...arr]  //first option
// console.log(arr2);
//----------------------------------------------------------------------------------
// var arr2 = []
// arr.forEach(function(value){
//     arr2.push(value)
// })
// console.log(arr2)  




// write a function to check if a number is evenn or odd 
// function evod(value){
//     if(value%2==0){
//         console.log("even")
//     }
//     else{
//         console.log("odd")
//     }
// }
// console.log(evod)




//create a function to calculate the area of circle with a given radius
// function area(r){
//     return Math.floor(Math.PI * r * r );
// }
// console.log(area(12))



//create a function that checks if a string starts with a specific character 
// function checker (str, char){
//     return str.toLoverCase().startsWith(char.toLoverCase);
// }
// console.log(checker("Umesh","U"));



//write a function to find the maximum number.
// function max(a,b){
//     if(a>b){
//         console.log("a is big")
//     }
//     else{
//         console.log("a is small")
//     }
// }
// console.log(max(5,7));



//create a function that take a number and returns its factorial
// function getfact(num){
//    let fact  = 1
//     for(i=1;i<=num;i++){
//          fact = fact * i;
//     }
//     return fact
// }
// console.log(getfact(5))



// //without using function
// let num = 5
// let fact = 1


// for(let i=1;i<=num;i++){
//     fact = fact * i;
// }
// console.log(fact);



//write a function  that accept a string and returns its reverse
// function abcd(value){
//     return value.split('').reverse().join('');
// }
// console.log(abcd("umesh"))



// //sorting number 
// let num = [5,4,6]
// num.sort((a,b)=>a-b);
// console.log(num);



//find the largest nuber in array
// function largearr(arr){
//     var max = 0;
//     for(var i = 0 ; i<arr.length;i++){
//         if(arr[i] > arr[max]){
//             max = i;
//         }
//     }
//     return arr[max]
// }
// console.log(largearr([1,2,3,8,6,]))



// palindrome in string 
// let str = "papa"
// let reverse = str.split('').reverse().join('')

// if(str === reverse){
//     console.log("palindrome")
// }
// else{
//     console.log("not palindrome")
// }



// palindrome in number
// let num = 1221;

// let reversenum = parseInt(num.toString().split('').reverse().join(''))

// if(num === reversenum ){
//     console.log("palindrome num")
// }
// else{
//     console.log("not palindrome number")
// }



let str = "paap"
let reversestr = str.split('').sort().join()

if(str === reversestr){
    console.log
}